import { Mail, Phone, Globe } from "lucide-react";

const CVHeader = () => {
  return (
    <div className="bg-cv-header text-cv-header-foreground p-8 print:p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-2 print:text-3xl">EFREN CRUZ AVILA</h1>
        <h2 className="text-xl font-medium mb-6 opacity-90 print:text-lg">Especialista en Nómina</h2>
        
        <div className="flex flex-wrap gap-6 text-sm print:text-xs">
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            <span>3311611322</span>
          </div>
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            <span>nominante.com</span>
          </div>
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            <span><EMAIL></span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CVHeader;
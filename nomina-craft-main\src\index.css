@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 95%;
    --foreground: 220 15% 15%;

    --card: 0 0% 98%;
    --card-foreground: 220 15% 15%;

    --popover: 0 0% 98%;
    --popover-foreground: 220 15% 15%;

    --primary: 217 91% 35%;
    --primary-foreground: 0 0% 98%;

    --secondary: 220 20% 85%;
    --secondary-foreground: 220 15% 15%;

    --muted: 220 20% 88%;
    --muted-foreground: 220 12% 40%;

    --accent: 142 76% 45%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 15% 80%;
    --input: 220 15% 85%;
    --ring: 217 91% 35%;

    /* Sistema de nómina moderno - Colores más vibrantes */
    --system-bg: 220 25% 92%;
    --system-card: 0 0% 98%;
    --system-primary: 217 91% 45%;
    --system-secondary: 262 83% 58%;
    --system-success: 142 76% 45%;
    --system-warning: 45 93% 55%;
    --system-error: 0 84% 60%;
    --system-border: 220 15% 75%;
    --system-text: 220 20% 20%;
    --system-text-muted: 220 15% 45%;
    --system-accent: 220 20% 88%;

    /* Colores específicos para CV */
    --cv-header: 217 91% 45%;
    --cv-header-foreground: 0 0% 98%;
    --cv-section: 0 0% 98%;
    --cv-text: 220 20% 20%;
    --cv-muted: 220 15% 45%;
    --cv-progress: 142 76% 45%;
    --cv-border: 220 15% 75%;
    --cv-highlight: 262 83% 58%;

    /* Gradientes del sistema */
    --gradient-system: linear-gradient(135deg, hsl(var(--system-primary)) 0%, hsl(var(--system-secondary)) 100%);
    --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);

    /* Sombras del sistema */
    --shadow-system: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-system-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    --radius: 0.5rem;

    --sidebar-background: 220 25% 92%;

    --sidebar-foreground: 220 20% 25%;

    --sidebar-primary: 217 91% 35%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 220 20% 85%;

    --sidebar-accent-foreground: 220 20% 25%;

    --sidebar-border: 220 15% 75%;

    --sidebar-ring: 217 91% 45%;
  }

  .dark {
    --background: 220 25% 8%;
    --foreground: 210 40% 95%;

    --card: 220 25% 12%;
    --card-foreground: 210 40% 95%;

    --popover: 220 25% 12%;
    --popover-foreground: 210 40% 95%;

    --primary: 217 91% 65%;
    --primary-foreground: 220 25% 8%;

    --secondary: 220 20% 20%;
    --secondary-foreground: 210 40% 95%;

    --muted: 220 20% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 142 76% 55%;
    --accent-foreground: 220 25% 8%;

    --destructive: 0 84% 65%;
    --destructive-foreground: 210 40% 95%;

    --border: 220 20% 25%;
    --input: 220 20% 20%;
    --ring: 217 91% 65%;

    /* Sistema de nómina moderno - Modo oscuro */
    --system-bg: 220 25% 8%;
    --system-card: 220 25% 12%;
    --system-primary: 217 91% 65%;
    --system-secondary: 262 83% 68%;
    --system-success: 142 76% 55%;
    --system-warning: 45 93% 65%;
    --system-error: 0 84% 65%;
    --system-border: 220 20% 25%;
    --system-text: 210 40% 90%;
    --system-text-muted: 220 15% 65%;
    --system-accent: 220 20% 18%;

    /* Colores específicos para CV - Modo oscuro */
    --cv-header: 217 91% 65%;
    --cv-header-foreground: 220 25% 8%;
    --cv-section: 220 25% 12%;
    --cv-text: 210 40% 90%;
    --cv-muted: 220 15% 65%;
    --cv-progress: 142 76% 55%;
    --cv-border: 220 20% 25%;
    --cv-highlight: 262 83% 68%;

    --sidebar-background: 220 25% 10%;
    --sidebar-foreground: 210 40% 90%;
    --sidebar-primary: 217 91% 65%;
    --sidebar-primary-foreground: 220 25% 8%;
    --sidebar-accent: 220 20% 18%;
    --sidebar-accent-foreground: 210 40% 90%;
    --sidebar-border: 220 20% 25%;
    --sidebar-ring: 217 91% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Print styles for PDF generation */
@media print {
  @page {
    margin: 0;
    size: A4;
  }
  
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  .print\\:hidden {
    display: none !important;
  }
}
import { Building2, Calendar, MapPin, TrendingUp } from "lucide-react";

interface SystemExperienceProps {
  company: string;
  position: string;
  period: string;
  location?: string;
  description: string;
  achievements: string[];
  systems?: string;
  type: "current" | "previous";
}

const SystemExperience = ({ 
  company, 
  position, 
  period,
  location = "Guadalajara, Jalisco",
  description, 
  achievements, 
  systems,
  type
}: SystemExperienceProps) => {
  return (
    <div className={`border-l-4 pl-6 pb-6 print:pl-4 print:pb-4 ${
      type === 'current' ? 'border-system-success' : 'border-system-primary'
    }`}>
      <div className="bg-system-card rounded-lg p-6 shadow-md print:p-4">
        {/* Header */}
        <div className="flex flex-wrap items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Building2 className="h-4 w-4 text-system-primary" />
              <h4 className="font-bold text-system-text text-lg print:text-base">{company}</h4>
              {type === 'current' && (
                <span className="bg-system-success/10 text-system-success px-2 py-1 rounded-full text-xs font-medium">
                  Actual
                </span>
              )}
            </div>
            <h5 className="font-semibold text-system-primary mb-1 print:text-sm">{position}</h5>
            <div className="flex flex-wrap items-center gap-4 text-xs text-system-text-muted print:text-[10px]">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{period}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{location}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Descripción */}
        <p className="text-system-text text-sm mb-4 print:text-xs print:mb-3 bg-system-accent/20 p-3 rounded-lg">
          {description}
        </p>
        
        {/* Logros */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-3">
            <TrendingUp className="h-4 w-4 text-system-success" />
            <h6 className="font-semibold text-system-text text-sm print:text-xs">Logros Principales</h6>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {achievements.map((achievement, index) => (
              <div key={index} className="flex items-start bg-system-accent/10 p-3 rounded-lg print:p-2">
                <div className="w-2 h-2 bg-system-success rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-system-text text-sm print:text-xs">{achievement}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Sistemas */}
        {systems && (
          <div className="bg-system-primary/5 p-3 rounded-lg print:p-2">
            <p className="text-system-text-muted text-xs print:text-[10px]">
              <span className="font-semibold text-system-primary">Tecnologías:</span> 
              <span className="ml-2">{systems}</span>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemExperience;
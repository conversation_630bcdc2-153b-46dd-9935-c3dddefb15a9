import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { generatePDF } from "@/utils/pdfGenerator";

const DownloadButton = () => {
  const handleDownload = () => {
    generatePDF('cv-document', 'CV_<PERSON><PERSON><PERSON>_Cruz_Avila.pdf');
  };

  return (
    <Button 
      onClick={handleDownload}
      className="fixed bottom-6 right-6 bg-system-primary hover:bg-system-primary/90 text-white shadow-lg print:hidden z-50"
      size="lg"
    >
      <Download className="h-5 w-5 mr-2" />
      Descargar PDF
    </Button>
  );
};

export default DownloadButton;
interface SystemMetricProps {
  label: string;
  value: string | number;
  trend?: "up" | "down" | "stable";
  color?: "primary" | "success" | "warning" | "error";
}

const SystemMetric = ({ label, value, trend, color = "primary" }: SystemMetricProps) => {
  const colorClasses = {
    primary: "text-system-primary bg-system-primary/10",
    success: "text-system-success bg-system-success/10", 
    warning: "text-system-warning bg-system-warning/10",
    error: "text-system-error bg-system-error/10"
  };

  return (
    <div className="flex items-center justify-between p-3 rounded-lg bg-system-accent/50 print:p-2">
      <div>
        <p className="text-xs text-system-text-muted print:text-[10px]">{label}</p>
        <p className="text-lg font-bold text-system-text print:text-sm">{value}</p>
      </div>
      <div className={`px-2 py-1 rounded-full text-xs font-medium ${colorClasses[color]} print:text-[10px]`}>
        {trend === "up" && "↗"}
        {trend === "down" && "↘"}
        {trend === "stable" && "→"}
        {!trend && "●"}
      </div>
    </div>
  );
};

export default SystemMetric;
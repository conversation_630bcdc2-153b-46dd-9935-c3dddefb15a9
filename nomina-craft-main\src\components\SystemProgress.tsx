interface SystemProgressProps {
  skill: string;
  percentage: number;
  level: "Básico" | "Intermedio" | "Avanzado" | "Experto";
  years?: number;
}

const SystemProgress = ({ skill, percentage, level, years }: SystemProgressProps) => {
  const levelColors = {
    "Básico": "bg-system-warning",
    "Intermedio": "bg-system-primary", 
    "Avanzado": "bg-system-secondary",
    "Experto": "bg-system-success"
  };

  return (
    <div className="bg-system-accent/30 rounded-lg p-4 print:p-3">
      <div className="flex justify-between items-start mb-3">
        <div>
          <h4 className="font-semibold text-system-text text-sm print:text-xs">{skill}</h4>
          <p className="text-xs text-system-text-muted print:text-[10px]">
            {level} {years && `• ${years} años`}
          </p>
        </div>
        <div className="text-right">
          <span className="text-lg font-bold text-system-text print:text-sm">{percentage}%</span>
        </div>
      </div>
      
      <div className="w-full bg-system-border rounded-full h-3 print:h-2">
        <div 
          className={`h-3 rounded-full transition-all duration-1000 ease-out print:h-2 ${levelColors[level]}`}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      <div className="mt-2 flex justify-between text-xs text-system-text-muted print:text-[10px]">
        <span>0%</span>
        <span>50%</span>
        <span>100%</span>
      </div>
    </div>
  );
};

export default SystemProgress;
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface SystemCardProps {
  title: string;
  children: React.ReactNode;
  badge?: string;
  className?: string;
}

const SystemCard = ({ title, children, badge, className = "" }: SystemCardProps) => {
  return (
    <Card className={`bg-system-card border-system-border shadow-lg hover:shadow-xl transition-all duration-300 ${className}`}>
      <div className="p-6 print:p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-system-text print:text-base">{title}</h3>
          {badge && (
            <Badge className="bg-system-primary/10 text-system-primary border-system-primary/20 print:text-xs">
              {badge}
            </Badge>
          )}
        </div>
        <div className="space-y-3 print:space-y-2">
          {children}
        </div>
      </div>
    </Card>
  );
};

export default SystemCard;
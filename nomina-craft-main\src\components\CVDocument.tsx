import SystemHeader from "./SystemHeader";
import SystemCard from "./SystemCard";
import SystemProgress from "./SystemProgress";
import SystemExperience from "./SystemExperience";
import SystemMetric from "./SystemMetric";
import { GraduationCap, Award, Clock, Users } from "lucide-react";

const CVDocument = () => {
  const skills = [
    { skill: "Cálculo de Nómina", percentage: 100, level: "Experto" as const, years: 10 },
    { skill: "Obligaciones Patronales", percentage: 95, level: "Experto" as const, years: 10 },
    { skill: "Aná<PERSON>is de Datos", percentage: 88, level: "Avanzado" as const, years: 8 },
    { skill: "Liderazgo de Equipos", percentage: 78, level: "Avanzado" as const, years: 6 },
    { skill: "Legislación Vigente", percentage: 90, level: "Experto" as const, years: 10 }
  ];

  const experiences = [
    {
      company: "GC Studio Legal & Account",
      position: "Coordinador de Nóminas",
      period: "Agosto 2021 – Noviembre 2024",
      description: "Responsable del área de nómina interna y del servicio de maquila para diversos clientes.",
      achievements: [
        "Administración operativa del ciclo completo de nómina",
        "Emisión de reportes e informes para dirección, contabilidad y clientes",
        "Asesoría en cumplimiento de obligaciones relacionadas con la nómina",
        "Acompañamiento a clientes en trámites ante autoridades"
      ],
      systems: "Contpaq i Nóminas, Worky, SUA, IDSE, REPSE, ICSOE, SISUB, Portal FONACOT, Portal Empresarial INFONAVIT",
      type: "previous" as const
    },
    {
      company: "Corporativo BOSSC",
      position: "Gerente de Nóminas", 
      period: "Mayo 2017 – Agosto 2021",
      description: "Implementé controles internos en el área de nómina y asumí la Gerencia tras unos meses.",
      achievements: [
        "Tomar requerimientos directamente con dirección y clientes",
        "Coordinar equipos para garantizar procesos puntuales",
        "Verificar provisiones, cargas y costos de facturación",
        "Migración de outsourcing a maquila manteniendo continuidad"
      ],
      systems: "Contpaq i Nóminas, SUA, IDSE, SATIC, SIROC",
      type: "previous" as const
    },
    {
      company: "Human Services 21",
      position: "Supervisor de Nóminas",
      period: "Octubre 2011 – Mayo 2017", 
      description: "Inicié como Analista y fui promovido a Supervisor tras reestructura por crecimiento.",
      achievements: [
        "Cálculo de nóminas y finiquitos",
        "Determinación de variables y obligaciones IMSS",
        "Generación de reportes periódicos",
        "Coordinación de equipos de nómina asignados"
      ],
      systems: "Contpaq i Nóminas, Giro",
      type: "previous" as const
    }
  ];

  return (
    <div id="cv-document" className="min-h-screen bg-system-bg">
      <SystemHeader />
      
      <div className="max-w-7xl mx-auto p-8 print:p-6">
        {/* Dashboard Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8 print:mb-6">
          <SystemCard title="Experiencia Total" className="md:col-span-1">
            <SystemMetric label="Años en Nómina" value="10+" color="success" trend="up" />
            <SystemMetric label="Empresas" value="3" color="primary" />
          </SystemCard>
          
          <SystemCard title="Especialización" className="md:col-span-1">
            <SystemMetric label="Sistemas Manejados" value="15+" color="warning" />
            <SystemMetric label="Equipos Dirigidos" value="5+" color="primary" />
          </SystemCard>
          
          <SystemCard title="Certificaciones" className="md:col-span-1">
            <SystemMetric label="IMSS/INFONAVIT" value="100%" color="success" />
            <SystemMetric label="SAT/REPSE" value="100%" color="success" />
          </SystemCard>
          
          <SystemCard title="Rendimiento" className="md:col-span-1">
            <SystemMetric label="Precisión" value="99.9%" color="success" trend="stable" />
            <SystemMetric label="Cumplimiento" value="100%" color="success" />
          </SystemCard>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 print:gap-6">
          {/* Contenido Principal */}
          <div className="lg:col-span-2 space-y-8 print:space-y-6">
            <SystemCard title="PERFIL PROFESIONAL" badge="Especialista">
              <div className="bg-system-accent/20 p-4 rounded-lg print:p-3">
                <p className="text-system-text leading-relaxed text-sm print:text-xs mb-3">
                  Más de 10 años haciendo nóminas. He trabajado en outsourcing, maquila de nómina y despacho fiscal, 
                  coordinando equipos, validando procesos, asesorando a clientes y siendo responsable del cálculo, 
                  timbrado y cumplimiento ante IMSS, INFONAVIT, FONACOT y SAT.
                </p>
                <p className="text-system-text leading-relaxed text-sm print:text-xs">
                  Manejo distintos volúmenes de empleados, esquemas y procesos. Conozco a fondo la lógica de nómina 
                  y me adapto a cualquier sistema con múltiples razones sociales y registros patronales.
                </p>
              </div>
            </SystemCard>

            <SystemCard title="HISTORIAL LABORAL" badge="3 Empresas">
              <div className="space-y-6 print:space-y-4">
                {experiences.map((exp, index) => (
                  <SystemExperience key={index} {...exp} />
                ))}
              </div>
            </SystemCard>
          </div>

          {/* Panel Lateral */}
          <div className="space-y-8 print:space-y-6">
            <SystemCard title="EDUCACIÓN" badge="Licenciatura">
              <div className="bg-gradient-to-br from-system-primary/10 to-system-secondary/10 p-4 rounded-lg print:p-3">
                <div className="flex items-start gap-3">
                  <div className="bg-system-primary text-white rounded-lg p-2 print:p-1">
                    <GraduationCap className="h-5 w-5 print:h-4 print:w-4" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-system-text text-sm print:text-xs mb-1">
                      Lic. en Trabajo Social (Pasante)
                    </h4>
                    <p className="text-system-text-muted text-xs print:text-[10px] mb-2">
                      Febrero 2006 – Febrero 2010
                    </p>
                    <div className="flex items-center gap-1 text-xs text-system-success">
                      <Award className="h-3 w-3" />
                      <span>Especialización en Recursos Humanos</span>
                    </div>
                  </div>
                </div>
              </div>
            </SystemCard>

            <SystemCard title="COMPETENCIAS TÉCNICAS" badge="5 Áreas">
              <div className="space-y-4 print:space-y-3">
                {skills.map((skill, index) => (
                  <SystemProgress key={index} {...skill} />
                ))}
              </div>
            </SystemCard>

            <SystemCard title="DATOS ADICIONALES">
              <div className="space-y-3 print:space-y-2">
                <div className="flex items-center gap-3 p-3 bg-system-accent/20 rounded-lg print:p-2">
                  <Clock className="h-4 w-4 text-system-primary" />
                  <div>
                    <p className="text-xs text-system-text-muted print:text-[10px]">Disponibilidad</p>
                    <p className="text-sm font-medium text-system-text print:text-xs">Inmediata</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-system-accent/20 rounded-lg print:p-2">
                  <Users className="h-4 w-4 text-system-primary" />
                  <div>
                    <p className="text-xs text-system-text-muted print:text-[10px]">Modalidad</p>
                    <p className="text-sm font-medium text-system-text print:text-xs">Presencial/Híbrido</p>
                  </div>
                </div>
              </div>
            </SystemCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CVDocument;
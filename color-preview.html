<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vista Previa de Colores Mejorados</title>
    <style>
        :root {
            /* Colores mejorados - más vibrantes y con mejor contraste */
            --background: hsl(0, 0%, 95%);
            --foreground: hsl(220, 15%, 15%);
            --card: hsl(0, 0%, 98%);
            --card-foreground: hsl(220, 15%, 15%);
            --primary: hsl(217, 91%, 45%);
            --primary-foreground: hsl(0, 0%, 98%);
            --secondary: hsl(220, 20%, 85%);
            --secondary-foreground: hsl(220, 15%, 15%);
            --muted: hsl(220, 20%, 88%);
            --muted-foreground: hsl(220, 12%, 40%);
            --accent: hsl(142, 76%, 45%);
            --accent-foreground: hsl(0, 0%, 98%);
            --border: hsl(220, 15%, 80%);
            
            /* Sistema de nómina moderno - Colores más vibrantes */
            --system-bg: hsl(220, 25%, 92%);
            --system-card: hsl(0, 0%, 98%);
            --system-primary: hsl(217, 91%, 45%);
            --system-secondary: hsl(262, 83%, 58%);
            --system-success: hsl(142, 76%, 45%);
            --system-warning: hsl(45, 93%, 55%);
            --system-error: hsl(0, 84%, 60%);
            --system-border: hsl(220, 15%, 75%);
            --system-text: hsl(220, 20%, 20%);
            --system-text-muted: hsl(220, 15%, 45%);
            --system-accent: hsl(220, 20%, 88%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--system-bg);
            color: var(--system-text);
            line-height: 1.6;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, var(--system-primary) 0%, var(--system-secondary) 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .color-card {
            background: var(--system-card);
            border: 1px solid var(--system-border);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .color-card h3 {
            color: var(--system-text);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .color-sample {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        .color-box {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 1px solid var(--system-border);
        }

        .color-info {
            flex: 1;
        }

        .color-name {
            font-weight: 600;
            color: var(--system-text);
            font-size: 0.9rem;
        }

        .color-value {
            color: var(--system-text-muted);
            font-size: 0.8rem;
            font-family: monospace;
        }

        .primary { background: var(--system-primary); }
        .secondary { background: var(--system-secondary); }
        .success { background: var(--system-success); }
        .warning { background: var(--system-warning); }
        .error { background: var(--system-error); }
        .accent { background: var(--system-accent); }
        .muted { background: var(--muted); }
        .card { background: var(--system-card); }
        .bg { background: var(--system-bg); }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }

        .before, .after {
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }

        .before {
            background: hsl(0, 0%, 98%);
            color: hsl(220, 13%, 18%);
            border: 1px solid hsl(220, 13%, 91%);
        }

        .after {
            background: var(--system-card);
            color: var(--system-text);
            border: 1px solid var(--system-border);
        }

        .sample-button {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            margin: 0.5rem;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: var(--system-primary);
            color: white;
        }

        .btn-secondary {
            background: var(--system-secondary);
            color: white;
        }

        .btn-success {
            background: var(--system-success);
            color: white;
        }

        .btn-old-primary {
            background: hsl(217, 91%, 20%);
            color: white;
        }

        .btn-old-secondary {
            background: hsl(220, 14%, 96%);
            color: hsl(220, 13%, 18%);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Colores Mejorados - CV Nómina</h1>
            <p>Los colores ahora tienen más contraste y son más vibrantes</p>
        </div>

        <div class="color-grid">
            <div class="color-card">
                <h3>🎯 Colores Principales</h3>
                <div class="color-sample">
                    <div class="color-box primary"></div>
                    <div class="color-info">
                        <div class="color-name">Primary</div>
                        <div class="color-value">hsl(217, 91%, 45%)</div>
                    </div>
                </div>
                <div class="color-sample">
                    <div class="color-box secondary"></div>
                    <div class="color-info">
                        <div class="color-name">Secondary</div>
                        <div class="color-value">hsl(262, 83%, 58%)</div>
                    </div>
                </div>
                <div class="color-sample">
                    <div class="color-box success"></div>
                    <div class="color-info">
                        <div class="color-name">Success</div>
                        <div class="color-value">hsl(142, 76%, 45%)</div>
                    </div>
                </div>
            </div>

            <div class="color-card">
                <h3>⚠️ Colores de Estado</h3>
                <div class="color-sample">
                    <div class="color-box warning"></div>
                    <div class="color-info">
                        <div class="color-name">Warning</div>
                        <div class="color-value">hsl(45, 93%, 55%)</div>
                    </div>
                </div>
                <div class="color-sample">
                    <div class="color-box error"></div>
                    <div class="color-info">
                        <div class="color-name">Error</div>
                        <div class="color-value">hsl(0, 84%, 60%)</div>
                    </div>
                </div>
            </div>

            <div class="color-card">
                <h3>🎨 Colores de Fondo</h3>
                <div class="color-sample">
                    <div class="color-box bg"></div>
                    <div class="color-info">
                        <div class="color-name">Background</div>
                        <div class="color-value">hsl(220, 25%, 92%)</div>
                    </div>
                </div>
                <div class="color-sample">
                    <div class="color-box card"></div>
                    <div class="color-info">
                        <div class="color-name">Card</div>
                        <div class="color-value">hsl(0, 0%, 98%)</div>
                    </div>
                </div>
                <div class="color-sample">
                    <div class="color-box accent"></div>
                    <div class="color-info">
                        <div class="color-name">Accent</div>
                        <div class="color-value">hsl(220, 20%, 88%)</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="comparison">
            <div class="before">
                <h3>❌ Antes (Muy Claro)</h3>
                <p>Los colores eran demasiado claros y tenían poco contraste</p>
                <button class="sample-button btn-old-primary">Primary Anterior</button>
                <button class="sample-button btn-old-secondary">Secondary Anterior</button>
            </div>
            <div class="after">
                <h3>✅ Después (Mejorado)</h3>
                <p>Ahora los colores son más vibrantes y tienen mejor contraste</p>
                <button class="sample-button btn-primary">Primary Nuevo</button>
                <button class="sample-button btn-secondary">Secondary Nuevo</button>
                <button class="sample-button btn-success">Success Nuevo</button>
            </div>
        </div>

        <div class="color-card" style="margin-top: 2rem;">
            <h3>📋 Resumen de Mejoras</h3>
            <ul style="list-style: none; padding: 0;">
                <li style="margin: 0.5rem 0; padding: 0.5rem; background: var(--system-accent); border-radius: 6px;">
                    ✨ <strong>Contraste mejorado:</strong> Los textos ahora son más legibles
                </li>
                <li style="margin: 0.5rem 0; padding: 0.5rem; background: var(--system-accent); border-radius: 6px;">
                    🎨 <strong>Colores más vibrantes:</strong> Reducida la luminosidad excesiva
                </li>
                <li style="margin: 0.5rem 0; padding: 0.5rem; background: var(--system-accent); border-radius: 6px;">
                    🌙 <strong>Modo oscuro mejorado:</strong> Colores consistentes para tema oscuro
                </li>
                <li style="margin: 0.5rem 0; padding: 0.5rem; background: var(--system-accent); border-radius: 6px;">
                    📱 <strong>Mejor accesibilidad:</strong> Cumple con estándares de contraste
                </li>
            </ul>
        </div>
    </div>
</body>
</html>

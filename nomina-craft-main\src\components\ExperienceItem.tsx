interface ExperienceItemProps {
  company: string;
  position: string;
  period: string;
  description: string;
  achievements: string[];
  systems?: string;
}

const ExperienceItem = ({ 
  company, 
  position, 
  period, 
  description, 
  achievements, 
  systems 
}: ExperienceItemProps) => {
  return (
    <div className="border-l-4 border-cv-progress pl-6 pb-6 print:pl-4 print:pb-4">
      <div className="mb-3">
        <h4 className="text-cv-section font-bold text-base print:text-sm">{company}</h4>
        <h5 className="text-cv-text font-semibold text-sm print:text-xs">{position}</h5>
        <p className="text-cv-muted text-sm print:text-xs">{period}</p>
      </div>
      
      <p className="text-cv-text text-sm mb-3 print:text-xs print:mb-2">{description}</p>
      
      <ul className="space-y-1 text-cv-text text-sm print:text-xs print:space-y-0.5">
        {achievements.map((achievement, index) => (
          <li key={index} className="flex items-start">
            <span className="text-cv-progress mr-2 font-bold">•</span>
            <span>{achievement}</span>
          </li>
        ))}
      </ul>
      
      {systems && (
        <div className="mt-3 print:mt-2">
          <p className="text-cv-muted text-xs print:text-[10px]">
            <span className="font-semibold">Sistemas utilizados:</span> {systems}
          </p>
        </div>
      )}
    </div>
  );
};

export default ExperienceItem;
interface SkillBarProps {
  skill: string;
  percentage: number;
}

const SkillBar = ({ skill, percentage }: SkillBarProps) => {
  return (
    <div className="mb-4 print:mb-3">
      <div className="flex justify-between items-center mb-2">
        <span className="text-cv-text font-medium text-sm print:text-xs">{skill}</span>
        <span className="text-cv-muted text-sm print:text-xs">{percentage}%</span>
      </div>
      <div className="w-full bg-cv-border rounded-full h-2.5 print:h-2">
        <div 
          className="bg-cv-progress h-2.5 rounded-full transition-all duration-1000 ease-out print:h-2"
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

export default SkillBar;
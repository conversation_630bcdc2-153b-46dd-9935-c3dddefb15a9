import { ReactNode } from "react";

interface CVSectionProps {
  title: string;
  children: ReactNode;
  className?: string;
}

const CVSection = ({ title, children, className = "" }: CVSectionProps) => {
  return (
    <div className={`mb-8 print:mb-6 ${className}`}>
      <h3 className="text-lg font-bold text-cv-section mb-4 pb-2 border-b-2 border-cv-border print:text-base print:mb-3">
        {title}
      </h3>
      <div className="space-y-4 print:space-y-3">
        {children}
      </div>
    </div>
  );
};

export default CVSection;
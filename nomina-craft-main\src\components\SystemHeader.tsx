import { Mail, Phone, Globe, MapPin, User, Calendar } from "lucide-react";
import SystemCard from "./SystemCard";
import SystemMetric from "./SystemMetric";
import profilePhoto from "@/assets/profile-photo.jpg";

const SystemHeader = () => {
  return (
    <div className="bg-gradient-to-r from-system-primary to-system-secondary p-8 print:p-6">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 items-center">
          {/* Foto de Perfil */}
          <div className="lg:col-span-1 flex justify-center lg:justify-start">
            <div className="relative">
              <div className="w-32 h-32 rounded-xl overflow-hidden border-4 border-white/20 shadow-lg print:w-24 print:h-24">
                <img 
                  src={profilePhoto} 
                  alt="<PERSON><PERSON><PERSON>" 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -bottom-2 -right-2 bg-system-success text-white rounded-full p-2 print:p-1">
                <User className="h-4 w-4 print:h-3 print:w-3" />
              </div>
            </div>
          </div>

          {/* Información Principal */}
          <div className="lg:col-span-2 text-center lg:text-left text-white">
            <div className="mb-4">
              <h1 className="text-3xl font-bold mb-1 print:text-2xl">EFREN CRUZ AVILA</h1>
              <p className="text-lg opacity-90 print:text-base">ID: EMP-2024-001</p>
              <h2 className="text-xl font-medium opacity-80 print:text-lg">Especialista en Nómina</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm print:text-xs">
              <div className="flex items-center gap-2 justify-center lg:justify-start">
                <Phone className="h-4 w-4" />
                <span>3311611322</span>
              </div>
              <div className="flex items-center gap-2 justify-center lg:justify-start">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2 justify-center lg:justify-start">
                <Globe className="h-4 w-4" />
                <span>nominante.com</span>
              </div>
              <div className="flex items-center gap-2 justify-center lg:justify-start">
                <MapPin className="h-4 w-4" />
                <span>Guadalajara, Jalisco, México</span>
              </div>
            </div>
          </div>

          {/* Métricas Rápidas */}
          <div className="lg:col-span-1">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 print:p-3">
              <h3 className="text-white font-semibold mb-3 text-sm print:text-xs">Métricas Clave</h3>
              <div className="space-y-3 print:space-y-2">
                <SystemMetric label="Experiencia" value="10+ años" color="success" />
                <SystemMetric label="Empresas" value="3" color="primary" />
                <SystemMetric label="Sistemas" value="15+" color="warning" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemHeader;